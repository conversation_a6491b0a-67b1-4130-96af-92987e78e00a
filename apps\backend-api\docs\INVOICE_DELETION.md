# Invoice Deletion and Task Management

## Overview

This document describes the enhanced invoice deletion functionality that properly cleans up all related data including agent sessions, Celery tasks, and action items.

## Problem Solved

Previously, deleting an invoice only removed the invoice record and file, leaving behind:
- Agent sessions and their execution data
- Running or pending Celery tasks
- Action items
- Orphaned database records

This could lead to:
- Memory leaks
- Continued processing of deleted invoices
- Inconsistent data state
- Resource waste

## New Functionality

### Enhanced Delete Invoice Endpoint

**Endpoint:** `DELETE /invoices/{invoice_id}`

The enhanced delete function now:

1. **Finds Related Agent Sessions**
   - Searches for sessions linked via `source_metadata.invoice_db_id`
   - Identifies all processing sessions for the invoice

2. **Cancels Active Tasks**
   - Revokes running Celery tasks using `celery_app.control.revoke()`
   - Updates session status to `CANCELLED`
   - Handles both main invoice tasks and session-specific tasks

3. **Cleans Up Action Items**
   - Deletes action items linked to the invoice
   - Deletes action items linked to related agent sessions

4. **Removes Agent Sessions**
   - Deletes sessions (cascade deletes execution steps, tool results, thought chains)

5. **Deletes Physical Files**
   - Removes uploaded invoice files from disk

6. **Deletes Invoice Record**
   - Removes invoice from database (cascade deletes accounting entries, vectors)

### New Endpoints

#### Get Active Tasks
**Endpoint:** `GET /invoices/{invoice_id}/active-tasks`

Returns information about active tasks for an invoice:
```json
{
  "invoice_id": "uuid",
  "invoice_status": "processing",
  "task_info": {
    "total_sessions": 2,
    "active_sessions": 1,
    "session_details": [
      {
        "session_id": "uuid",
        "status": "RUNNING",
        "created_at": "2024-01-01T10:00:00Z",
        "task_ids": ["task-id-1", "task-id-2"]
      }
    ]
  }
}
```

#### Cancel Tasks Without Deletion
**Endpoint:** `POST /invoices/{invoice_id}/cancel-tasks`

Cancels active tasks for an invoice without deleting the invoice:
```json
{
  "message": "Successfully cancelled 2 active tasks",
  "cancelled_sessions": 2,
  "invoice_id": "uuid"
}
```

## Task Cleanup Service

### TaskCleanupService Class

Located in `app/services/task_cleanup_service.py`, this service handles:

- **Task Discovery**: Finds Celery task IDs from multiple sources
- **Task Revocation**: Safely cancels running tasks
- **Session Management**: Updates session statuses
- **Error Handling**: Graceful handling of cleanup failures

### Key Methods

- `cleanup_invoice_related_data()`: Main cleanup method
- `get_active_tasks_for_invoice()`: Get task information
- `_cancel_session_and_tasks()`: Cancel individual sessions
- `_find_session_task_ids()`: Discover task IDs

## Task ID Tracking

### Enhanced Task Storage

When invoices are processed, task IDs are now stored in:

1. **Invoice.raw_data**: Main processing task ID
2. **AgentSession.source_metadata**: Session-specific task IDs
3. **ToolResult.result_data**: Tool-specific task IDs

### Example Storage
```json
// Invoice.raw_data
{
  "celery_task_id": "ai_agent_process_invoice_task-uuid",
  "other_data": "..."
}

// AgentSession.source_metadata
{
  "invoice_db_id": "invoice-uuid",
  "celery_task_id": "session-task-uuid",
  "file_path": "/path/to/file"
}
```

## Relationship Between Invoices and Agent Sessions

### Data Flow

1. **Invoice Upload/Sync**
   - Invoice record created
   - Celery task started (`ai_agent_process_invoice_task`)
   - Task ID stored in `invoice.raw_data`

2. **Agent Session Creation**
   - Session created with `source_metadata.invoice_db_id`
   - Links session to invoice
   - Additional task IDs may be generated

3. **Processing**
   - Multiple execution steps may create sub-tasks
   - Action items may be created for human review
   - All linked via session and invoice relationships

4. **Cleanup on Deletion**
   - All related data found via relationships
   - Tasks cancelled before data deletion
   - Ensures clean removal

### Database Relationships

```
Invoice (1) ←→ (N) AgentSession (via source_metadata)
Invoice (1) ←→ (N) ActionItem (direct FK)
AgentSession (1) ←→ (N) ActionItem (direct FK)
AgentSession (1) ←→ (N) ExecutionStep (cascade delete)
AgentSession (1) ←→ (N) ToolResult (cascade delete)
AgentSession (1) ←→ (N) ThoughtChain (cascade delete)
```

## Usage Examples

### Check Active Tasks Before Deletion
```bash
curl -X GET "http://localhost:8000/invoices/{invoice_id}/active-tasks" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Cancel Tasks Without Deletion
```bash
curl -X POST "http://localhost:8000/invoices/{invoice_id}/cancel-tasks" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Delete Invoice with Full Cleanup
```bash
curl -X DELETE "http://localhost:8000/invoices/{invoice_id}" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Response Example
```json
{
  "message": "Invoice and all related data deleted successfully",
  "cancelled_sessions": 2,
  "deleted_action_items": 3,
  "revoked_tasks": 4,
  "cleanup_errors": []
}
```

## Error Handling

The cleanup process is designed to be resilient:

- **Partial Failures**: Continues cleanup even if some tasks fail to cancel
- **Error Reporting**: Returns list of any errors encountered
- **Transaction Safety**: Uses database transactions for consistency
- **Logging**: Comprehensive logging for debugging

## Permissions

All new endpoints respect existing permission system:

- `INVOICES_DELETE`: Required for deletion
- `INVOICES_WRITE`: Required for task cancellation
- `INVOICES_READ`: Required for viewing active tasks

## Monitoring

Enhanced logging provides visibility into:
- Task cancellation attempts
- Session cleanup progress
- File deletion status
- Error conditions

Check application logs for detailed cleanup information.
