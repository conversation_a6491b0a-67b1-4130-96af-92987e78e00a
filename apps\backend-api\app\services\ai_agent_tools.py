from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type
from sqlalchemy.orm import Session
import logging
import asyncio
import json
import uuid
from datetime import datetime, timedelta, timezone

from app.services.integration_service import IntegrationService
from app.services.vector_service import get_vector_service
from app.database import set_tenant_context

logger = logging.getLogger(__name__)


class BaseTool(ABC):
    """Base class for all AI agent tools"""
    
    def __init__(self, db: Session, tenant_id: str):
        self.db = db
        self.tenant_id = tenant_id
        set_tenant_context(db, tenant_id)
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the tool with given input data"""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Tool name"""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Tool description"""
        pass


class ToolRegistry:
    """Registry for managing AI agent tools"""
    
    def __init__(self):
        self._tools: Dict[str, Type[BaseTool]] = {}
    
    def register(self, name: str, tool_class: Type[BaseTool]):
        """Register a tool"""
        self._tools[name] = tool_class
        logger.info(f"Registered tool: {name}")
    
    def get_tool(self, name: str) -> Optional[Type[BaseTool]]:
        """Get a tool by name"""
        return self._tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all registered tools"""
        return list(self._tools.keys())


class GetInvoiceTool(BaseTool):
    """Tool for fetching invoices from various sources"""
    
    @property
    def name(self) -> str:
        return "get_invoice"
    
    @property
    def description(self) -> str:
        return "Fetch invoice from configured integration (Fortnox, eEkonomi, HTTP, etc.)"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch invoice using appropriate integration"""
        try:
            session_context = input_data.get("session_context", {})
            source_type = session_context.get("source_type", "manual")
            source_metadata = session_context.get("source_metadata", {})
            invoice_unique_id = session_context.get("invoice_unique_id")
            
            integration_service = IntegrationService(self.db)
            
            if source_type == "erp":
                # Fetch from ERP integration
                result = await self._fetch_from_erp(integration_service, invoice_unique_id, source_metadata)
            elif source_type == "http":
                # Fetch from HTTP endpoint
                result = await self._fetch_from_http(integration_service, source_metadata)
            else:
                # Manual or other sources
                result = await self._fetch_manual(invoice_unique_id, source_metadata)
            
            return {
                "success": True,
                "invoice_data": result,
                "source_type": source_type,
                "invoice_id": invoice_unique_id
            }
            
        except Exception as e:
            logger.error(f"Error in GetInvoiceTool: {e}")
            return {
                "success": False,
                "error": str(e),
                "source_type": source_type
            }
    
    async def _fetch_from_erp(self, integration_service: IntegrationService, invoice_id: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch invoice from ERP system"""
        # Use existing integration service to fetch from configured ERP
        # Note: sync_all_tenant_integrations might not support specific_invoice_id parameter
        # This is a placeholder implementation that should be adapted based on actual integration service API
        try:
            from datetime import datetime, timedelta

            # Fetch recent invoices and filter by ID
            sync_result = await integration_service.sync_all_tenant_integrations(
                self.tenant_id,
                datetime.now(timezone.utc) - timedelta(days=30)  # Look back 30 days
            )

            invoices = sync_result.get("invoices", [])

            # Find invoice by ID
            for invoice in invoices:
                if str(invoice.get("id")) == str(invoice_id) or str(invoice.get("invoice_id")) == str(invoice_id):
                    return invoice

            raise ValueError(f"Invoice {invoice_id} not found in ERP system")

        except Exception as e:
            logger.error(f"Error fetching invoice {invoice_id} from ERP: {e}")
            # Return metadata as fallback
            return {
                "id": invoice_id,
                "source": "erp",
                "metadata": metadata,
                "error": str(e)
            }
    
    async def _fetch_from_http(self, integration_service: IntegrationService, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch invoice from HTTP endpoint"""
        url = metadata.get("url")
        if not url:
            raise ValueError("HTTP URL not provided in metadata")
        
        # Use HTTP integration to fetch
        # This would use the existing HTTP integration logic
        return {"url": url, "status": "fetched", "content": "HTTP content"}
    
    async def _fetch_manual(self, invoice_id: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Handle manual invoice processing"""
        try:
            # For manual uploads, get the invoice from database using invoice_db_id
            invoice_db_id = metadata.get("invoice_db_id")

            if not invoice_db_id:
                raise ValueError("No invoice_db_id provided in metadata for manual upload")

            # Import here to avoid circular imports
            from app.models.invoice import Invoice

            # Get invoice from database
            invoice = self.db.query(Invoice).filter(
                Invoice.id == invoice_db_id,
                Invoice.tenant_id == self.tenant_id
            ).first()

            if not invoice:
                raise ValueError(f"Invoice {invoice_db_id} not found in database")

            # Return invoice data in expected format
            return {
                "id": str(invoice.id),
                "invoice_id": invoice_id,
                "supplier_name": invoice.supplier_name,
                "invoice_number": invoice.invoice_number,
                "invoice_date": invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "total_amount": float(invoice.total_amount) if invoice.total_amount else None,
                "currency": invoice.currency,
                "file_path": invoice.file_path,
                "file_type": invoice.file_type,
                "original_filename": invoice.original_filename,
                "status": invoice.status,
                "type": "manual",
                "metadata": metadata
            }

        except Exception as e:
            logger.error(f"Error fetching manual invoice: {e}")
            # Return basic data as fallback
            return {
                "invoice_id": invoice_id,
                "type": "manual",
                "metadata": metadata,
                "error": str(e)
            }


class ExtractContentTool(BaseTool):
    """Tool for extracting and determining file format"""
    
    @property
    def name(self) -> str:
        return "extract_content"
    
    @property
    def description(self) -> str:
        return "Determine file format and extract invoice content appropriately"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract content from invoice file"""
        try:
            previous_results = input_data.get("previous_results", {})
            invoice_data = previous_results.get("get_invoice", {}).get("invoice_data", {})
            
            if not invoice_data:
                raise ValueError("No invoice data from previous step")
            
            # Get file information
            file_path = invoice_data.get("file_path")
            file_type = invoice_data.get("file_type", "").lower()
            
            if not file_path:
                raise ValueError("No file path provided in invoice data")
            
            # Use OCR service to extract text
            from app.services.ocr_service import OCRService
            ocr_service = OCRService()
            extracted_text = ocr_service.extract_text_from_file(file_path, file_type)

            # Use LLM to extract structured context
            from app.services.llm_provider import get_provider_instance
            llm_provider = get_provider_instance()
            structured_context = await llm_provider.extract_context(extracted_text)
            
            return {
                "success": True,
                "file_type": file_type,
                "extracted_text": extracted_text,
                "structured_context": structured_context,
                "file_path": file_path
            }
            
        except Exception as e:
            logger.error(f"Error in ExtractContentTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }


class WebSearchTool(BaseTool):
    """Tool for enriching context with web search"""
    
    @property
    def name(self) -> str:
        return "web_search"
    
    @property
    def description(self) -> str:
        return "Enrich invoice context by searching for supplier and product information"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform web search to enrich context"""
        try:
            previous_results = input_data.get("previous_results", {})
            extract_result = previous_results.get("extract_content", {})
            structured_context = extract_result.get("structured_context", {})
            
            if not structured_context:
                raise ValueError("No structured context from previous step")
            
            # Extract search terms from context
            supplier_name = self._extract_supplier_name(structured_context)
            products = self._extract_products(structured_context)
            
            # Perform searches
            search_results = {}
            
            if supplier_name:
                supplier_info = await self._search_supplier(supplier_name)
                search_results["supplier"] = supplier_info
            
            if products:
                product_info = await self._search_products(products)
                search_results["products"] = product_info
            
            return {
                "success": True,
                "search_results": search_results,
                "enriched_context": {
                    **structured_context,
                    "web_search_data": search_results
                }
            }
            
        except Exception as e:
            logger.error(f"Error in WebSearchTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _extract_supplier_name(self, context: Dict[str, Any]) -> Optional[str]:
        """Extract supplier name from structured context"""
        # This would parse the structured context to find supplier information
        return context.get("supplier_name") or context.get("vendor_name")
    
    def _extract_products(self, context: Dict[str, Any]) -> List[str]:
        """Extract product names from structured context"""
        # This would parse the structured context to find product information
        items = context.get("line_items", [])
        products = []
        for item in items:
            if isinstance(item, dict) and "description" in item:
                products.append(item["description"])
        return products
    
    async def _search_supplier(self, supplier_name: str) -> Dict[str, Any]:
        """Search for supplier information"""
        try:
            import requests
            from app.config import settings

            # Use Google Custom Search API if configured
            if hasattr(settings, 'google_search_api_key') and hasattr(settings, 'google_search_engine_id'):
                search_query = f"{supplier_name} company business"
                url = "https://www.googleapis.com/customsearch/v1"
                params = {
                    'key': settings.google_search_api_key,
                    'cx': settings.google_search_engine_id,
                    'q': search_query,
                    'num': 3
                }

                response = requests.get(url, params=params, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    items = data.get('items', [])

                    # Extract information from search results
                    websites = []
                    descriptions = []
                    for item in items:
                        if 'link' in item:
                            websites.append(item['link'])
                        if 'snippet' in item:
                            descriptions.append(item['snippet'])

                    return {
                        "name": supplier_name,
                        "websites": websites[:2],  # Top 2 websites
                        "descriptions": descriptions[:2],  # Top 2 descriptions
                        "search_performed": True,
                        "search_query": search_query
                    }

            # Fallback to mock data if API not configured
            return {
                "name": supplier_name,
                "industry": "Unknown",
                "website": "Unknown",
                "search_performed": False,
                "note": "Google Search API not configured"
            }

        except Exception as e:
            logger.warning(f"Error searching for supplier {supplier_name}: {e}")
            return {
                "name": supplier_name,
                "error": str(e),
                "search_performed": False
            }

    async def _search_products(self, products: List[str]) -> List[Dict[str, Any]]:
        """Search for product information"""
        try:
            import requests
            from app.config import settings

            results = []

            # Use Google Custom Search API if configured
            if hasattr(settings, 'google_search_api_key') and hasattr(settings, 'google_search_engine_id'):
                for product in products[:3]:  # Limit to 3 products to avoid API limits
                    try:
                        search_query = f"{product} product category type"
                        url = "https://www.googleapis.com/customsearch/v1"
                        params = {
                            'key': settings.google_search_api_key,
                            'cx': settings.google_search_engine_id,
                            'q': search_query,
                            'num': 2
                        }

                        response = requests.get(url, params=params, timeout=10)
                        if response.status_code == 200:
                            data = response.json()
                            items = data.get('items', [])

                            descriptions = []
                            for item in items:
                                if 'snippet' in item:
                                    descriptions.append(item['snippet'])

                            results.append({
                                "product": product,
                                "descriptions": descriptions,
                                "search_performed": True,
                                "search_query": search_query
                            })
                        else:
                            results.append({
                                "product": product,
                                "error": f"Search API returned {response.status_code}",
                                "search_performed": False
                            })

                    except Exception as product_error:
                        results.append({
                            "product": product,
                            "error": str(product_error),
                            "search_performed": False
                        })
            else:
                # Fallback to mock data if API not configured
                results = [
                    {
                        "product": product,
                        "category": "Unknown",
                        "search_performed": False,
                        "note": "Google Search API not configured"
                    }
                    for product in products
                ]

            return results

        except Exception as e:
            logger.warning(f"Error searching for products: {e}")
            return [
                {
                    "product": product,
                    "error": str(e),
                    "search_performed": False
                }
                for product in products
            ]


class RAGSearchTool(BaseTool):
    """Tool for RAG search to determine accounting accounts"""
    
    @property
    def name(self) -> str:
        return "rag_search"
    
    @property
    def description(self) -> str:
        return "Use RAG search against vector database to determine appropriate accounting accounts"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform RAG search for accounting suggestions"""
        try:
            previous_results = input_data.get("previous_results", {})
            web_search_result = previous_results.get("web_search", {})
            enriched_context = web_search_result.get("enriched_context", {})
            
            if not enriched_context:
                raise ValueError("No enriched context from previous step")
            
            # Use vector service for similarity search
            vector_service = get_vector_service()
            
            # Create search query from enriched context
            search_query = self._create_search_query(enriched_context)
            
            # Find similar invoices
            similar_contexts = await vector_service.get_similar_contexts(
                self.db, 
                self.tenant_id, 
                search_query,
                limit=5
            )
            
            # Use LLM to suggest accounting entries
            from app.services.llm_provider import get_provider_instance
            llm_provider = get_provider_instance()
            accounting_suggestions = await llm_provider.suggest_accounting(
                json.dumps(enriched_context),
                similar_contexts
            )
            
            return {
                "success": True,
                "similar_contexts_count": len(similar_contexts),
                "accounting_suggestions": accounting_suggestions,
                "search_query": search_query
            }
            
        except Exception as e:
            logger.error(f"Error in RAGSearchTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _create_search_query(self, enriched_context: Dict[str, Any]) -> str:
        """Create search query from enriched context"""
        query_parts = []
        
        # Add supplier information
        if "supplier_name" in enriched_context:
            query_parts.append(f"Supplier: {enriched_context['supplier_name']}")
        
        # Add product/service information
        web_search_data = enriched_context.get("web_search_data", {})
        if "products" in web_search_data:
            products = [p.get("product", "") for p in web_search_data["products"]]
            if products:
                query_parts.append(f"Products: {', '.join(products)}")
        
        # Add amount information
        if "total_amount" in enriched_context:
            query_parts.append(f"Amount: {enriched_context['total_amount']}")
        
        return " | ".join(query_parts)


class BookInvoiceTool(BaseTool):
    """Tool for booking the invoice"""
    
    @property
    def name(self) -> str:
        return "book_invoice"
    
    @property
    def description(self) -> str:
        return "Book the invoice using the determined accounting entries"
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Book the invoice"""
        try:
            previous_results = input_data.get("previous_results", {})
            rag_result = previous_results.get("rag_search", {})
            accounting_suggestions = rag_result.get("accounting_suggestions", {})
            
            if not accounting_suggestions:
                raise ValueError("No accounting suggestions from previous step")
            
            # Check confidence level
            overall_confidence = accounting_suggestions.get("overall_confidence", 0)
            confidence_threshold = 0.8  # This could be configurable
            
            if overall_confidence < confidence_threshold:
                return {
                    "success": False,
                    "requires_review": True,
                    "reason": f"Confidence level {overall_confidence} below threshold {confidence_threshold}",
                    "accounting_suggestions": accounting_suggestions
                }
            
            # TODO: Implement actual booking logic
            # This would integrate with the accounting system or create accounting entries
            
            return {
                "success": True,
                "booked": True,
                "confidence": overall_confidence,
                "accounting_entries": accounting_suggestions.get("entries", [])
            }
            
        except Exception as e:
            logger.error(f"Error in BookInvoiceTool: {e}")
            return {
                "success": False,
                "error": str(e)
            }
