from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
import os
import aiofiles
import logging

from app.database import get_db
from app.models.invoice import Invoice, AccountingEntry
from app.models.agent_session import AgentSession, SessionStatus
from app.models.action_item import ActionItem
from app.models.user import TenantUser
from app.middleware import get_current_tenant_user
from app.utils.permissions import Permission, check_permission
from app.utils.security import generate_secure_filename
from app.config import settings
from app.tasks.ai_agent_tasks import ai_agent_process_invoice_task
from app.services.duplicate_detection import DuplicateDetectionService
from app.services.task_cleanup_service import TaskCleanupService
from app.celery_app import celery_app

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/upload")
async def upload_invoice(
    file: UploadFile = File(...),
    supplier_name: Optional[str] = Form(None),
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Upload and process an invoice file"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Validate file type
    allowed_types = ["application/pdf", "image/png", "image/jpeg", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File type not supported. Please upload PDF, PNG, or JPG files."
        )
    
    # Validate file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size is {settings.max_file_size / (1024*1024):.1f}MB"
        )
    
    # Generate secure filename
    secure_filename = generate_secure_filename(file.filename)
    file_path = os.path.join(settings.upload_dir, str(tenant_user.tenant_id), secure_filename)
    
    # Ensure upload directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # Save file
    try:
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save file"
        )

    # Create content hash for duplicate detection
    duplicate_service = DuplicateDetectionService(db)
    content_hash = duplicate_service.create_content_hash(file_path)
    
    # Create invoice record
    invoice = Invoice(
        tenant_id=tenant_user.tenant_id,
        supplier_name=supplier_name or "Unknown",
        original_filename=file.filename,
        file_path=file_path,
        file_type=file.content_type.split('/')[-1],
        status="pending"
    )
    
    db.add(invoice)
    db.commit()
    db.refresh(invoice)
    
    # Prepare source metadata for AI agent
    source_metadata = {
        "file_path": file_path,
        "file_type": file.content_type.split('/')[-1],
        "filename": file.filename,
        "file_size": file.size,
        "content_hash": content_hash,
        "supplier_name": supplier_name,
        "upload_user_id": str(tenant_user.user_id),
        "invoice_db_id": str(invoice.id)  # Link to invoice record
    }

    # Start AI agent processing and store task ID
    task = ai_agent_process_invoice_task.delay(
        tenant_id=str(tenant_user.tenant_id),
        execution_plan_name="invoice_processing_v1",
        invoice_unique_id=None,  # No unique ID for manual uploads
        source_type="manual",
        source_metadata=source_metadata
    )

    # Store the task ID in the invoice for easier cleanup
    invoice.raw_data = invoice.raw_data or {}
    invoice.raw_data["celery_task_id"] = task.id
    db.commit()

    return {
        "id": invoice.id,
        "message": "Invoice uploaded successfully and AI agent processing started",
        "status": invoice.status,
        "processing_type": "ai_agent"
    }


@router.get("/", response_model=List[dict])
async def list_invoices(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """List invoices for current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    query = db.query(Invoice).filter(Invoice.tenant_id == tenant_user.tenant_id)
    
    if status_filter:
        query = query.filter(Invoice.status == status_filter)
    
    invoices = query.offset(skip).limit(limit).all()
    
    return [
        {
            "id": invoice.id,
            "supplier_name": invoice.supplier_name,
            "invoice_number": invoice.invoice_number,
            "total_amount": invoice.total_amount,
            "currency": invoice.currency,
            "status": invoice.status,
            "created_at": invoice.created_at,
            "original_filename": invoice.original_filename
        }
        for invoice in invoices
    ]


@router.get("/{invoice_id}")
async def get_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Get invoice details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Get accounting entries
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    return {
        "id": invoice.id,
        "supplier_name": invoice.supplier_name,
        "invoice_number": invoice.invoice_number,
        "invoice_date": invoice.invoice_date,
        "due_date": invoice.due_date,
        "total_amount": invoice.total_amount,
        "currency": invoice.currency,
        "status": invoice.status,
        "extracted_text": invoice.extracted_text,
        "extracted_context": invoice.extracted_context,
        "processing_error": invoice.processing_error,
        "created_at": invoice.created_at,
        "updated_at": invoice.updated_at,
        "original_filename": invoice.original_filename,
        "accounting_entries": [
            {
                "id": entry.id,
                "account_code": entry.account_code,
                "account_name": entry.account_name,
                "debit_amount": entry.debit_amount,
                "credit_amount": entry.credit_amount,
                "description": entry.description,
                "confidence_score": entry.confidence_score,
                "is_validated": entry.is_validated
            }
            for entry in accounting_entries
        ]
    }


@router.put("/{invoice_id}/validate")
async def validate_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Validate invoice accounting entries"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACCOUNTING_VALIDATE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Update accounting entries as validated
    accounting_entries = db.query(AccountingEntry).filter(
        AccountingEntry.invoice_id == invoice_id,
        AccountingEntry.tenant_id == tenant_user.tenant_id
    ).all()
    
    for entry in accounting_entries:
        entry.is_validated = True
        entry.validated_by = tenant_user.user_id
    
    # Update invoice status
    invoice.status = "completed"
    
    db.commit()
    
    return {"message": "Invoice validated successfully"}


@router.get("/{invoice_id}/active-tasks")
async def get_invoice_active_tasks(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Get information about active tasks for an invoice"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()

    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )

    cleanup_service = TaskCleanupService(db)
    task_info = cleanup_service.get_active_tasks_for_invoice(invoice_id, tenant_user.tenant_id)

    return {
        "invoice_id": str(invoice_id),
        "invoice_status": invoice.status,
        "task_info": task_info
    }


@router.post("/{invoice_id}/cancel-tasks")
async def cancel_invoice_tasks(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Cancel active tasks for an invoice without deleting the invoice"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()

    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )

    try:
        cleanup_service = TaskCleanupService(db)

        # Get task info before cancellation
        task_info = cleanup_service.get_active_tasks_for_invoice(invoice_id, tenant_user.tenant_id)

        if task_info["active_sessions"] == 0:
            return {
                "message": "No active tasks to cancel",
                "task_info": task_info
            }

        # Find and cancel only active sessions (don't delete them)
        related_sessions = cleanup_service._find_related_sessions(invoice_id, tenant_user.tenant_id)
        cancelled_count = 0

        for session in related_sessions:
            if session.status in [SessionStatus.PENDING, SessionStatus.RUNNING]:
                cleanup_service._cancel_session_and_tasks(session)
                cancelled_count += 1

        db.commit()

        logger.info(f"Cancelled {cancelled_count} active tasks for invoice {invoice_id}")

        return {
            "message": f"Successfully cancelled {cancelled_count} active tasks",
            "cancelled_sessions": cancelled_count,
            "invoice_id": str(invoice_id)
        }

    except Exception as e:
        logger.error(f"Error cancelling tasks for invoice {invoice_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel tasks: {str(e)}"
        )


@router.delete("/{invoice_id}")
async def delete_invoice(
    invoice_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """
    Delete an invoice and all related data including:
    - Agent sessions and their execution steps
    - Celery tasks (pending/running)
    - Action items
    - Files
    - Related database entries (cascade deleted automatically)
    """
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_DELETE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    invoice = db.query(Invoice).filter(
        Invoice.id == invoice_id,
        Invoice.tenant_id == tenant_user.tenant_id
    ).first()

    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )

    logger.info(f"Starting deletion of invoice {invoice_id} and related data")

    try:
        # Use the cleanup service to handle all related data
        cleanup_service = TaskCleanupService(db)
        cleanup_stats = cleanup_service.cleanup_invoice_related_data(invoice_id, tenant_user.tenant_id)

        # Delete the physical file
        try:
            if invoice.file_path and os.path.exists(invoice.file_path):
                os.remove(invoice.file_path)
                logger.info(f"Deleted file: {invoice.file_path}")
        except Exception as e:
            logger.warning(f"Could not delete file {invoice.file_path}: {e}")
            # Continue even if file deletion fails

        # Delete the invoice (this will cascade delete accounting entries, vectors, etc.)
        db.delete(invoice)

        # Commit all changes
        db.commit()

        logger.info(f"Successfully deleted invoice {invoice_id} and all related data")

        return {
            "message": "Invoice and all related data deleted successfully",
            "cancelled_sessions": cleanup_stats["cancelled_sessions"],
            "deleted_action_items": cleanup_stats["deleted_action_items"],
            "revoked_tasks": cleanup_stats["revoked_tasks"],
            "cleanup_errors": cleanup_stats["errors"]
        }

    except Exception as e:
        logger.error(f"Error deleting invoice {invoice_id}: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete invoice: {str(e)}"
        )
