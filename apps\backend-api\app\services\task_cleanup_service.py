"""
Service for cleaning up Celery tasks and related data when invoices are deleted
"""

import logging
from typing import List, Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from app.celery_app import celery_app
from app.models.agent_session import AgentSession, SessionStatus
from app.models.action_item import ActionItem
from app.models.invoice import Invoice

logger = logging.getLogger(__name__)


class TaskCleanupService:
    """Service for cleaning up tasks and related data when invoices are deleted"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def cleanup_invoice_related_data(self, invoice_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """
        Clean up all data related to an invoice including:
        - Agent sessions and their tasks
        - Action items
        - Celery tasks

        Returns:
            Dict with cleanup statistics
        """
        cleanup_stats = {
            "cancelled_sessions": 0,
            "revoked_tasks": 0,
            "deleted_action_items": 0,
            "errors": []
        }

        try:
            # 1. Get the invoice to check for task IDs
            invoice = self.db.query(Invoice).filter(
                Invoice.id == invoice_id,
                Invoice.tenant_id == tenant_id
            ).first()

            # 2. Revoke any task IDs stored in the invoice
            if invoice and invoice.raw_data:
                task_id = invoice.raw_data.get("celery_task_id")
                if task_id:
                    try:
                        logger.info(f"Revoking main invoice task {task_id}")
                        celery_app.control.revoke(task_id, terminate=True)
                        cleanup_stats["revoked_tasks"] += 1
                    except Exception as e:
                        error_msg = f"Error revoking main task {task_id}: {e}"
                        logger.error(error_msg)
                        cleanup_stats["errors"].append(error_msg)

            # 3. Find related agent sessions
            related_sessions = self._find_related_sessions(invoice_id, tenant_id)
            logger.info(f"Found {len(related_sessions)} related agent sessions for invoice {invoice_id}")

            # 4. Cancel running sessions and revoke tasks
            for session in related_sessions:
                if session.status in [SessionStatus.PENDING, SessionStatus.RUNNING]:
                    try:
                        revoked_count = self._cancel_session_and_tasks(session)
                        cleanup_stats["cancelled_sessions"] += 1
                        cleanup_stats["revoked_tasks"] += revoked_count
                    except Exception as e:
                        error_msg = f"Error cancelling session {session.id}: {e}"
                        logger.error(error_msg)
                        cleanup_stats["errors"].append(error_msg)

            # 5. Delete action items
            deleted_action_items = self._delete_related_action_items(invoice_id, tenant_id, related_sessions)
            cleanup_stats["deleted_action_items"] = deleted_action_items

            # 6. Delete agent sessions (cascade deletes execution steps, tool results, etc.)
            for session in related_sessions:
                try:
                    self.db.delete(session)
                    logger.info(f"Deleted agent session {session.id}")
                except Exception as e:
                    error_msg = f"Error deleting session {session.id}: {e}"
                    logger.error(error_msg)
                    cleanup_stats["errors"].append(error_msg)
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error in cleanup_invoice_related_data: {e}")
            cleanup_stats["errors"].append(str(e))
            return cleanup_stats
    
    def _find_related_sessions(self, invoice_id: UUID, tenant_id: UUID) -> List[AgentSession]:
        """Find agent sessions related to the invoice"""
        return self.db.query(AgentSession).filter(
            AgentSession.tenant_id == tenant_id,
            AgentSession.source_metadata.op('->>')('invoice_db_id') == str(invoice_id)
        ).all()
    
    def _cancel_session_and_tasks(self, session: AgentSession) -> int:
        """Cancel a session and revoke its Celery tasks. Returns number of revoked tasks."""
        logger.info(f"Cancelling session {session.id} with status {session.status}")

        # Try to find and revoke Celery tasks
        task_ids = self._find_session_task_ids(session)
        revoked_count = 0

        for task_id in task_ids:
            try:
                logger.info(f"Revoking Celery task {task_id}")
                celery_app.control.revoke(task_id, terminate=True)
                revoked_count += 1
            except Exception as e:
                logger.warning(f"Could not revoke Celery task {task_id}: {e}")

        # Update session status
        session.status = SessionStatus.CANCELLED
        session.completed_at = self.db.func.now()

        return revoked_count
    
    def _find_session_task_ids(self, session: AgentSession) -> List[str]:
        """Find Celery task IDs associated with a session"""
        task_ids = []
        
        # Check source metadata for task ID
        source_metadata = session.source_metadata or {}
        if 'celery_task_id' in source_metadata:
            task_ids.append(source_metadata['celery_task_id'])
        
        # Check for task IDs in execution steps or tool results
        for step in session.execution_steps:
            for tool_result in step.tool_results:
                result_data = tool_result.result_data or {}
                if 'task_id' in result_data:
                    task_ids.append(result_data['task_id'])
        
        return task_ids
    
    def _delete_related_action_items(self, invoice_id: UUID, tenant_id: UUID, sessions: List[AgentSession]) -> int:
        """Delete action items related to the invoice and its sessions"""
        deleted_count = 0
        
        # Delete action items directly linked to the invoice
        invoice_action_items = self.db.query(ActionItem).filter(
            ActionItem.tenant_id == tenant_id,
            ActionItem.invoice_id == invoice_id
        ).all()
        
        for action_item in invoice_action_items:
            self.db.delete(action_item)
            deleted_count += 1
        
        # Delete action items linked to agent sessions
        for session in sessions:
            session_action_items = self.db.query(ActionItem).filter(
                ActionItem.tenant_id == tenant_id,
                ActionItem.agent_session_id == session.id
            ).all()
            
            for action_item in session_action_items:
                self.db.delete(action_item)
                deleted_count += 1
        
        logger.info(f"Deleted {deleted_count} action items")
        return deleted_count
    
    def get_active_tasks_for_invoice(self, invoice_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """Get information about active tasks for an invoice"""
        related_sessions = self._find_related_sessions(invoice_id, tenant_id)
        
        active_sessions = [
            session for session in related_sessions 
            if session.status in [SessionStatus.PENDING, SessionStatus.RUNNING]
        ]
        
        task_info = {
            "total_sessions": len(related_sessions),
            "active_sessions": len(active_sessions),
            "session_details": []
        }
        
        for session in active_sessions:
            task_ids = self._find_session_task_ids(session)
            task_info["session_details"].append({
                "session_id": str(session.id),
                "status": session.status.value,
                "created_at": session.created_at.isoformat() if session.created_at else None,
                "task_ids": task_ids
            })
        
        return task_info
