#!/usr/bin/env python3
"""
Script to check AI Agent sessions and invoice processing status
"""

import requests
import json
from datetime import datetime
import sys

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def check_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Is the backend running?")
        return False

def get_ai_agent_sessions():
    """Get AI Agent sessions"""
    try:
        response = requests.get(f"{API_BASE}/ai-agent/sessions")
        if response.status_code == 200:
            sessions = response.json()
            print(f"\n📋 Found {len(sessions)} AI Agent sessions:")
            
            for session in sessions[:10]:  # Show last 10
                status_emoji = {
                    'pending': '⏳',
                    'running': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'cancelled': '🚫'
                }.get(session['status'], '❓')
                
                print(f"  {status_emoji} {session['session_name']} - {session['status']}")
                print(f"     Created: {session['created_at']}")
                if session.get('invoice_unique_id'):
                    print(f"     Invoice ID: {session['invoice_unique_id']}")
                print()
                
            return sessions
        else:
            print(f"❌ Failed to get sessions: {response.status_code}")
            if response.status_code == 401:
                print("   Authentication required. You need to login first.")
            return []
    except Exception as e:
        print(f"❌ Error getting sessions: {e}")
        return []

def get_invoices():
    """Get invoices"""
    try:
        response = requests.get(f"{API_BASE}/invoices/")
        if response.status_code == 200:
            invoices = response.json()
            print(f"\n📄 Found {len(invoices)} invoices:")
            
            for invoice in invoices[:10]:  # Show last 10
                status_emoji = {
                    'pending': '⏳',
                    'processing': '🔄',
                    'completed': '✅',
                    'failed': '❌',
                    'needs_review': '⚠️'
                }.get(invoice['status'], '❓')
                
                amount = f"{invoice.get('total_amount', 'N/A')} {invoice.get('currency', '')}"
                print(f"  {status_emoji} {invoice['supplier_name']} - {amount}")
                print(f"     Status: {invoice['status']}")
                print(f"     Created: {invoice['created_at']}")
                print()
                
            return invoices
        else:
            print(f"❌ Failed to get invoices: {response.status_code}")
            if response.status_code == 401:
                print("   Authentication required. You need to login first.")
            return []
    except Exception as e:
        print(f"❌ Error getting invoices: {e}")
        return []

def get_ai_agent_metrics():
    """Get AI Agent metrics"""
    try:
        response = requests.get(f"{API_BASE}/ai-agent/metrics")
        if response.status_code == 200:
            metrics = response.json()
            print(f"\n📊 AI Agent Metrics (last 30 days):")
            
            summary = metrics.get('session_summary', {})
            print(f"  Total sessions: {summary.get('total_sessions', 0)}")
            print(f"  Completed: {summary.get('completed_sessions', 0)}")
            print(f"  Failed: {summary.get('failed_sessions', 0)}")
            print(f"  Running: {summary.get('running_sessions', 0)}")
            print(f"  Pending: {summary.get('pending_sessions', 0)}")
            
            return metrics
        else:
            print(f"❌ Failed to get metrics: {response.status_code}")
            if response.status_code == 401:
                print("   Authentication required. You need to login first.")
            return {}
    except Exception as e:
        print(f"❌ Error getting metrics: {e}")
        return {}

def main():
    print("🔍 Checking AI Agent and Invoice Status")
    print("=" * 50)
    
    # Check if API is running
    if not check_health():
        sys.exit(1)
    
    # Get data
    sessions = get_ai_agent_sessions()
    invoices = get_invoices()
    metrics = get_ai_agent_metrics()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print(f"  • API Status: Running")
    print(f"  • AI Agent Sessions: {len(sessions)}")
    print(f"  • Invoices: {len(invoices)}")
    
    if not sessions and not invoices:
        print("\n💡 TIP: If you just ran Manual Sync but don't see any data,")
        print("   you might need to authenticate first or check if the sync")
        print("   actually created any sessions/invoices.")
        print("\n   Try logging into the frontend app first, then run this script again.")

if __name__ == "__main__":
    main()
