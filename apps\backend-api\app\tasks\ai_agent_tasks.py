"""
AI Agent Tasks for processing invoices
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from celery import current_task
from sqlalchemy.orm import Session

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context

from app.models.agent_session import AgentSession, SessionStatus
from app.services.ai_agent_orchestrator import AIAgentOrchestrator, DuplicateInvoiceError
from app.tasks.notifications import send_action_item_notification_task

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def ai_agent_process_invoice_task(
    self, 
    tenant_id: str,
    execution_plan_name: str,
    invoice_unique_id: Optional[str] = None,
    source_type: str = "manual",
    source_metadata: Optional[Dict[str, Any]] = None
):
    """
    AI Agent task for processing invoices with full orchestration

    This is the main task for all invoice processing in the system
    """
    logger.info(f"🚀 AI Agent task started for tenant {tenant_id}")
    db = SessionLocal()
    session_id = None
    
    try:
        # Set tenant context
        set_tenant_context(db, tenant_id)
        
        # Log progress
        logger.info(f"Initializing AI agent for tenant {tenant_id} with plan {execution_plan_name}")
        
        # Create AI Agent Orchestrator
        orchestrator = AIAgentOrchestrator(db)
        
        # Create session
        logger.info(f"Creating AI agent session for invoice {invoice_unique_id}")
        
        session = orchestrator.create_session(
            tenant_id=tenant_id,
            execution_plan_name=execution_plan_name,
            invoice_unique_id=invoice_unique_id,
            source_type=source_type,
            source_metadata=source_metadata or {}
        )
        
        session_id = str(session.id)
        logger.info(f"Created AI agent session {session_id} for tenant {tenant_id}")
        
        # Execute session
        logger.info(f"Executing AI agent session {session_id}")
        
        # Execute session using sync version
        session = orchestrator.execute_session(session_id)
        logger.info(f"AI Agent execution completed for session {session_id}, final status: {session.status}")

        # Return result based on session status
        if session.status == SessionStatus.COMPLETED:
            logger.info(f"AI agent session {session_id} completed successfully")
            return {
                "success": True,
                "session_id": session_id,
                "status": "completed",
                "tenant_id": tenant_id
            }
            
        elif session.status == SessionStatus.FAILED:
            # Handle failed session - create action item
            if session.requires_human_review:
                # Send notification for human review (will create action item)
                send_action_item_notification_task.delay(
                    tenant_id=tenant_id,
                    session_id=session_id,
                    reason=session.action_item_reason
                )
            
            # Log failure
            logger.error(f"AI agent session {session_id} failed: {session.action_item_reason}")
            
            return {
                "success": False,
                "session_id": session_id,
                "status": "failed",
                "reason": session.action_item_reason,
                "requires_review": session.requires_human_review
            }

        else:
            # Unexpected status
            logger.warning(f"AI agent session {session_id} ended with unexpected status: {session.status}")
            return {
                "success": False,
                "session_id": session_id,
                "status": str(session.status),
                "reason": "Unexpected session status"
            }
            
    except DuplicateInvoiceError as e:
        # Handle duplicate invoice error
        logger.warning(f"Duplicate invoice detected: {e}")
        
        if session_id:
            # Send notification about duplicate (will create action item)
            send_action_item_notification_task.delay(
                tenant_id=tenant_id,
                session_id=session_id,
                reason=f"Duplicate invoice: {str(e)}"
            )
        
        # Log duplicate error instead of using update_state
        logger.warning(f"Duplicate invoice detected for session {session_id}: {str(e)}")
        
        return {
            "success": False,
            "error": str(e),
            "error_type": "duplicate",
            "session_id": session_id
        }
        
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error in AI agent task: {e}", exc_info=True)

        if session_id:
            # Try to mark session as failed
            try:
                session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
                if session:
                    session.status = SessionStatus.FAILED
                    session.requires_human_review = True
                    session.action_item_reason = f"Task error: {str(e)}"
                    session.completed_at = datetime.utcnow()
                    db.commit()
                    
                    # Send notification (will create action item)
                    try:
                        send_action_item_notification_task.delay(
                            tenant_id=tenant_id,
                            session_id=session_id,
                            reason=f"Task error: {str(e)}"
                        )
                    except Exception as notification_error:
                        logger.error(f"Error sending notification: {notification_error}")
                        
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        
        # Don't use update_state to avoid Celery backend issues
        logger.error(f"AI Agent task failed for session {session_id}: {str(e)}")
        
        return {
            "success": False,
            "error": str(e),
            "error_type": "unexpected",
            "session_id": session_id
        }
        
    finally:
        db.close()


@celery_app.task(bind=True)
def ai_agent_fetch_invoices_task(self):
    """
    Task to fetch invoices from all integrations and start AI agent processing
    """
    logger.info("Starting invoice fetch from all integrations")
    
    try:
        # This would contain the actual fetch logic
        # For now, return success
        logger.info("Invoice fetch completed successfully")
        
        return {
            "success": True,
            "message": "Invoice fetch completed"
        }
        
    except Exception as e:
        logger.error(f"Error in invoice fetch task: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }


@celery_app.task(bind=True)
def retry_failed_session_task(self, session_id: str):
    """Retry a failed AI agent session"""
    db = SessionLocal()
    
    try:
        # Get the session
        session = db.query(AgentSession).filter(AgentSession.id == session_id).first()
        
        if not session:
            logger.error(f"Session {session_id} not found")
            return {
                "success": False,
                "error": "Session not found"
            }
        
        # Set tenant context
        set_tenant_context(db, str(session.tenant_id))
        
        # Reset session for retry
        session.status = SessionStatus.PENDING
        session.current_step_index = 0
        session.retry_count = 0
        session.requires_human_review = False
        session.action_item_reason = None
        session.started_at = None
        session.completed_at = None
        
        db.commit()
        
        # Create AI Agent Orchestrator and execute
        orchestrator = AIAgentOrchestrator(db)

        # Execute session using sync version (no asyncio needed)
        logger.info(f"Starting AI Agent execution for session {session_id}")
        session = orchestrator.execute_session(session_id)
        logger.info(f"AI Agent execution completed for session {session_id}, final status: {session.status}")
        
        logger.info(f"Retried session {session_id} with status: {session.status}")
        
        return {
            "success": True,
            "session_id": session_id,
            "status": str(session.status),
            "requires_review": session.requires_human_review
        }
        
    except Exception as e:
        logger.error(f"Error retrying session {session_id}: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }
        
    finally:
        db.close()
