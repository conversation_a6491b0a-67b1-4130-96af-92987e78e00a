#!/usr/bin/env python3
"""
Script to test AI Agent execution directly
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """Get authentication token by logging in"""
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        response = requests.post(f"{API_BASE}/auth/token", json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def create_ai_agent_session():
    """Create a new AI Agent session for an existing invoice"""
    print("🤖 Creating AI Agent session for existing invoice")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "X-Tenant-ID": "94311644-a034-4967-87ea-bc46068ab6be"
    }
    
    try:
        # Get the first invoice
        response = requests.get(f"{API_BASE}/invoices/", headers=headers)
        if response.status_code == 200:
            invoices = response.json()
            if invoices:
                invoice = invoices[0]
                print(f"Found invoice: {invoice['supplier_name']} (ID: {invoice['id']})")
                
                # Create AI Agent session
                session_data = {
                    "execution_plan_name": "invoice_processing_v1",
                    "invoice_unique_id": None,  # Manual upload
                    "source_type": "manual",
                    "source_metadata": {
                        "file_path": invoice.get("file_path"),
                        "file_type": invoice.get("file_type"),
                        "filename": invoice.get("original_filename"),
                        "supplier_name": invoice.get("supplier_name"),
                        "invoice_db_id": invoice["id"]
                    }
                }
                
                response = requests.post(f"{API_BASE}/ai-agent/sessions", json=session_data, headers=headers)
                
                print(f"Status Code: {response.status_code}")
                print(f"Response: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ AI Agent session created successfully!")
                    print(f"Task ID: {result.get('task_id')}")
                    return result.get('task_id')
                else:
                    print(f"❌ AI Agent session creation failed: {response.status_code}")
                    
            else:
                print("No invoices found")
        else:
            print(f"❌ Failed to get invoices: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error creating AI Agent session: {e}")

def main():
    print("🚀 AI Agent Execution Test")
    print("=" * 50)
    
    create_ai_agent_session()
    
    print("\n💡 Check the Celery Worker logs to see AI Agent execution!")
    print("   docker logs aggie-worker-1 --follow")

if __name__ == "__main__":
    main()
