# Invoice Deletion UI Features

## Overview

This document describes the frontend UI features for invoice deletion and task management in both app-frontend and admin-frontend.

## Features Added

### 1. Delete Invoice Button
- **Location**: Actions column in the invoices table
- **Icon**: Trash icon (TrashIcon from Heroicons)
- **Permission**: Requires `invoices:delete` permission
- **Styling**: Red color scheme with hover effects

### 2. Cancel Tasks Button
- **Location**: Actions column in the invoices table (next to delete button)
- **Icon**: Stop icon (StopIcon from Heroicons)
- **Permission**: Requires `invoices:write` permission
- **Visibility**: Only shown for invoices with status `processing` or `pending`
- **Styling**: Orange color scheme with hover effects

### 3. Smart Delete Confirmation
The delete function now:
- Checks for active tasks before deletion
- Shows different confirmation messages based on active tasks
- Warns users about task cancellation
- Provides detailed information about what will be deleted

### 4. Loading States
Both buttons show loading indicators when operations are in progress:
- Spinning animation replaces the icon
- But<PERSON> becomes disabled
- Tooltip text changes to indicate loading state

## User Experience Flow

### Deleting an Invoice

1. **User clicks delete button** (trash icon)
2. **System checks for active tasks** via API call to `/invoices/{id}/active-tasks`
3. **Confirmation dialog shows**:
   - If active tasks exist: Warning about task cancellation
   - If no active tasks: Standard deletion confirmation
4. **User confirms**: Invoice and all related data deleted
5. **Success notification**: Shows deletion statistics

### Cancelling Tasks

1. **User clicks cancel tasks button** (stop icon) - only visible for processing/pending invoices
2. **System checks for active tasks**
3. **Confirmation dialog**: Shows number of tasks to cancel
4. **User confirms**: Tasks cancelled, invoice remains
5. **Success notification**: Shows cancellation statistics

## API Integration

### New API Methods Added

```typescript
// In both app-frontend and admin-frontend services/api.ts

invoicesApi = {
  // ... existing methods
  
  getActiveTasks: async (id: string) => {
    const response = await api.get(`/invoices/${id}/active-tasks`);
    return response.data;
  },

  cancelTasks: async (id: string) => {
    const response = await api.post(`/invoices/${id}/cancel-tasks`);
    return response.data;
  }
}
```

### React Query Mutations

```typescript
// Delete mutation with enhanced success message
const deleteMutation = useMutation(
  (invoiceId: string) => invoicesApi.deleteInvoice(invoiceId),
  {
    onSuccess: (data) => {
      queryClient.invalidateQueries(['invoices']);
      toast.success(
        `Invoice deleted successfully! ${data.cancelled_sessions || 0} sessions cancelled, ${data.deleted_action_items || 0} action items removed.`
      );
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Delete failed');
    },
  }
);

// Cancel tasks mutation
const cancelTasksMutation = useMutation(
  (invoiceId: string) => invoicesApi.cancelTasks(invoiceId),
  {
    onSuccess: (data) => {
      queryClient.invalidateQueries(['invoices']);
      toast.success(`Cancelled ${data.cancelled_sessions || 0} active tasks for invoice.`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to cancel tasks');
    },
  }
);
```

## UI Components

### Button Styling

```tsx
// Delete button
<button
  onClick={() => handleDeleteInvoice(invoice)}
  disabled={deleteMutation.isLoading}
  className={`p-1 rounded-md transition-colors ${
    deleteMutation.isLoading
      ? 'text-gray-400 cursor-not-allowed'
      : 'text-red-600 hover:text-red-900 hover:bg-red-50'
  }`}
  title={deleteMutation.isLoading ? 'Deleting...' : 'Delete invoice'}
>
  {deleteMutation.isLoading ? (
    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-red-600"></div>
  ) : (
    <TrashIcon className="h-4 w-4" />
  )}
</button>

// Cancel tasks button
<button
  onClick={() => handleCancelTasks(invoice)}
  disabled={cancelTasksMutation.isLoading}
  className={`p-1 rounded-md transition-colors ${
    cancelTasksMutation.isLoading
      ? 'text-gray-400 cursor-not-allowed'
      : 'text-orange-600 hover:text-orange-900 hover:bg-orange-50'
  }`}
  title={cancelTasksMutation.isLoading ? 'Cancelling...' : 'Cancel active tasks'}
>
  {cancelTasksMutation.isLoading ? (
    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-orange-600"></div>
  ) : (
    <StopIcon className="h-4 w-4" />
  )}
</button>
```

## Error Handling

- **Network errors**: Graceful fallback to basic confirmation
- **Permission errors**: Buttons hidden based on user permissions
- **API errors**: User-friendly error messages via toast notifications
- **Loading states**: Prevent multiple simultaneous operations

## Permissions

- **Delete button**: Requires `invoices:delete` permission
- **Cancel tasks button**: Requires `invoices:write` permission
- **Validate button**: Requires `accounting:validate` permission (existing)

## Responsive Design

- Buttons are arranged in a flex container with proper spacing
- Icons are consistently sized (h-4 w-4)
- Hover effects provide visual feedback
- Loading states are clearly indicated

## Accessibility

- Proper ARIA labels and titles
- Keyboard navigation support
- Screen reader friendly
- Clear visual feedback for all states

## Testing Considerations

When testing these features:

1. **Test with different user roles** to verify permission-based visibility
2. **Test with invoices in different states** (pending, processing, completed)
3. **Test network failures** to ensure graceful error handling
4. **Test concurrent operations** to verify loading states work correctly
5. **Test the confirmation dialogs** with both active and inactive tasks
