from typing import Dict, Any, List, Optional, Type
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime
import logging
import json
import traceback
import uuid

from app.models.agent_session import (
    AgentSession, ExecutionPlan, ExecutionStep, ToolResult, ThoughtChain,
    SessionStatus, StepStatus
)
from app.services.ai_agent_tools import ToolRegistry, BaseTool
from app.services.duplicate_detection import DuplicateDetectionService
from app.database import set_tenant_context

logger = logging.getLogger(__name__)


class AIAgentOrchestrator:
    """Main orchestrator for AI Agent execution sessions"""
    
    def __init__(self, db: Session):
        self.db = db
        self.tool_registry = ToolRegistry()
        self.duplicate_service = DuplicateDetectionService(db)
        self._register_default_tools()
    
    def _register_default_tools(self):
        """Register all available tools"""
        from app.services.ai_agent_tools import (
            GetInvoiceTool, WebSearchTool, RAGSearchTool, 
            ExtractContentTool, BookInvoiceTool
        )
        
        self.tool_registry.register("get_invoice", GetInvoiceTool)
        self.tool_registry.register("extract_content", ExtractContentTool)
        self.tool_registry.register("web_search", WebSearchTool)
        self.tool_registry.register("rag_search", RAGSearchTool)
        self.tool_registry.register("book_invoice", BookInvoiceTool)
    
    def create_session(
        self,
        tenant_id: str,
        execution_plan_name: str,
        invoice_unique_id: Optional[str] = None,
        source_type: str = "manual",
        source_metadata: Optional[Dict[str, Any]] = None
    ) -> AgentSession:
        """Create a new AI agent session"""
        try:
            # Set tenant context
            set_tenant_context(self.db, tenant_id)
            
            # Check for duplicates using comprehensive detection
            existing_session = self.duplicate_service.check_duplicate_invoice(
                tenant_id=tenant_id,
                invoice_unique_id=invoice_unique_id,
                source_type=source_type,
                source_metadata=source_metadata
            )

            if existing_session:
                raise DuplicateInvoiceError(
                    f"Duplicate invoice detected. "
                    f"Invoice {invoice_unique_id or 'unknown'} already processed in session {existing_session.id} "
                    f"(status: {existing_session.status}, source: {existing_session.source_type})"
                )
            
            # Get execution plan
            execution_plan = self.db.query(ExecutionPlan).filter(
                ExecutionPlan.name == execution_plan_name,
                ExecutionPlan.is_active == True
            ).first()
            
            if not execution_plan:
                raise ValueError(f"Execution plan '{execution_plan_name}' not found or inactive")
            
            # Create session
            session = AgentSession(
                tenant_id=uuid.UUID(tenant_id),
                session_name=f"{execution_plan_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                execution_plan_id=execution_plan.id,
                invoice_unique_id=invoice_unique_id,
                source_type=source_type,
                source_metadata=source_metadata or {}
            )
            
            self.db.add(session)
            self.db.commit()
            self.db.refresh(session)
            
            # Create execution steps from plan
            self._create_execution_steps(session, execution_plan)
            
            # Log initial thought
            self._log_thought_sync(
                session.id,
                None,
                "plan",
                {
                    "message": "Session created successfully",
                    "execution_plan": execution_plan_name,
                    "steps_count": len(execution_plan.steps),
                    "invoice_id": invoice_unique_id,
                    "source_type": source_type
                }
            )
            
            logger.info(f"Created AI agent session {session.id} for tenant {tenant_id}")
            return session
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating AI agent session: {e}")
            raise
    
    def _check_duplicate_invoice(self, tenant_id: str, invoice_unique_id: str) -> Optional[AgentSession]:
        """
        Check if invoice has already been processed

        Returns existing session if:
        - Same invoice_unique_id exists for this tenant
        - Session status is COMPLETED or RUNNING
        - This prevents processing the same invoice multiple times
        """
        existing_session = self.db.query(AgentSession).filter(
            AgentSession.tenant_id == uuid.UUID(tenant_id),
            AgentSession.invoice_unique_id == invoice_unique_id,
            AgentSession.status.in_([SessionStatus.COMPLETED, SessionStatus.RUNNING])
        ).first()

        if existing_session:
            logger.warning(
                f"Duplicate invoice detected: {invoice_unique_id} already processed "
                f"in session {existing_session.id} with status {existing_session.status}"
            )

        return existing_session
    
    def _create_execution_steps(self, session: AgentSession, execution_plan: ExecutionPlan):
        """Create execution steps from execution plan"""
        for index, step_config in enumerate(execution_plan.steps):
            step = ExecutionStep(
                session_id=session.id,
                tenant_id=session.tenant_id,
                step_name=step_config["step"],
                step_index=index,
                step_config=step_config
            )
            self.db.add(step)
        
        self.db.commit()
    
    def execute_session(self, session_id: str) -> AgentSession:
        """Execute an AI agent session"""
        logger.info(f"Starting execution for session {session_id}")

        session = self.db.query(AgentSession).filter(AgentSession.id == session_id).first()
        if not session:
            raise ValueError(f"Session {session_id} not found")

        logger.info(f"Found session {session_id}, current status: {session.status}")

        # Set tenant context
        set_tenant_context(self.db, str(session.tenant_id))

        try:
            # Update session status
            session.status = SessionStatus.RUNNING
            session.started_at = datetime.utcnow()
            self.db.commit()

            logger.info(f"Updated session {session_id} status to RUNNING")

            self._log_thought_sync(
                session.id,
                None,
                "observation",
                {"message": "Starting session execution", "current_step": session.current_step_index}
            )

            logger.info(f"Session {session_id} has {len(session.execution_steps)} steps to execute")
            
            # Execute steps
            logger.info(f"Starting step execution loop for session {session_id}")
            logger.info(f"Session has {len(session.execution_steps)} steps to execute")

            while session.current_step_index < len(session.execution_steps):
                current_step = session.execution_steps[session.current_step_index]

                logger.info(f"Executing step {session.current_step_index}: {current_step.step_name}")

                try:
                    self._execute_step_sync(session, current_step)
                    session.current_step_index += 1
                    self.db.commit()  # Commit after each step
                    logger.info(f"Step {current_step.step_name} completed, moving to step {session.current_step_index}")

                except Exception as step_error:
                    logger.error(f"Step {current_step.step_name} failed: {step_error}", exc_info=True)
                    self._handle_step_error_sync(session, current_step, step_error)
                    break
            
            # Check if all steps completed successfully
            logger.info(f"Step execution loop finished. Current step index: {session.current_step_index}, Total steps: {len(session.execution_steps)}")

            if session.current_step_index >= len(session.execution_steps):
                logger.info(f"All steps completed! Marking session {session_id} as COMPLETED")
                session.status = SessionStatus.COMPLETED
                session.completed_at = datetime.utcnow()

                self._log_thought_sync(
                    session.id,
                    None,
                    "observation",
                    {"message": "Session completed successfully", "total_steps": len(session.execution_steps)}
                )
            else:
                logger.warning(f"Session {session_id} did not complete all steps. Current: {session.current_step_index}, Total: {len(session.execution_steps)}")
            
            self.db.commit()
            return session
            
        except Exception as e:
            self._handle_session_error_sync(session, e)
            return session

    def _execute_step_sync(self, session: AgentSession, step: ExecutionStep):
        """Execute a single step (sync version)"""
        step.status = StepStatus.RUNNING
        step.started_at = datetime.utcnow()
        self.db.commit()

        self._log_thought_sync(
            session.id,
            step.id,
            "reasoning",
            {
                "message": f"Starting step: {step.step_name}",
                "step_config": step.step_config,
                "step_index": step.step_index
            }
        )

        # Get tool for this step
        tool_name = step.step_config["step"]
        tool_class = self.tool_registry.get_tool(tool_name)

        if not tool_class:
            raise ValueError(f"Tool '{tool_name}' not found in registry")

        # Create tool instance
        tool = tool_class(self.db, str(session.tenant_id))

        # Prepare input data
        input_data = {
            "session_id": str(session.id),
            "step_id": str(step.id),
            "tenant_id": str(session.tenant_id),
            "invoice_unique_id": session.invoice_unique_id,
            "source_type": session.source_type,
            "source_metadata": session.source_metadata,
            "previous_results": self._get_previous_results(session, step.step_index)
        }

        # Execute tool using nest_asyncio to handle async/sync conflicts
        try:
            import nest_asyncio
            import asyncio

            # Apply nest_asyncio to allow nested event loops
            nest_asyncio.apply()

            # Execute the async tool
            result = asyncio.run(tool.execute(input_data))
            logger.info(f"Tool {tool_name} executed successfully")
        except Exception as tool_error:
            logger.error(f"Tool {tool_name} execution failed: {tool_error}")
            raise tool_error

        # Store result
        tool_result = ToolResult(
            session_id=session.id,
            step_id=step.id,
            tenant_id=session.tenant_id,
            tool_name=tool_name,
            input_data=input_data,
            output_data=result,
            success=result.get("success", False),
            error_message=result.get("error") if not result.get("success", False) else None
        )

        self.db.add(tool_result)

        # Update step status
        if result.get("success", False):
            step.status = StepStatus.COMPLETED
            step.completed_at = datetime.utcnow()

            self._log_thought_sync(
                session.id,
                step.id,
                "action",
                {
                    "message": f"Step completed: {step.step_name}",
                    "result": result,
                    "success": True
                }
            )
        else:
            step.status = StepStatus.FAILED
            step.completed_at = datetime.utcnow()
            error_msg = result.get("error", "Unknown error")

            self._log_thought_sync(
                session.id,
                step.id,
                "observation",
                {
                    "message": f"Step failed: {step.step_name}",
                    "error": error_msg,
                    "success": False
                }
            )

            raise Exception(f"Step {step.step_name} failed: {error_msg}")

        self.db.commit()

    def _get_previous_results(self, session: AgentSession, current_step_index: int) -> Dict[str, Any]:
        """Get results from previous steps"""
        previous_results = {}

        # Get all completed steps before current step
        completed_steps = [step for step in session.execution_steps
                          if step.step_index < current_step_index and step.status == StepStatus.COMPLETED]

        for step in completed_steps:
            # Get tool result for this step
            tool_result = self.db.query(ToolResult).filter(
                ToolResult.session_id == session.id,
                ToolResult.step_id == step.id
            ).first()

            if tool_result and tool_result.success:
                previous_results[tool_result.tool_name] = tool_result.output_data

        return previous_results

    async def _execute_step(self, session: AgentSession, step: ExecutionStep):
        """Execute a single step"""
        step.status = StepStatus.RUNNING
        step.started_at = datetime.utcnow()
        self.db.commit()
        
        await self._log_thought(
            session.id, 
            step.id,
            "reasoning", 
            {
                "message": f"Starting step: {step.step_name}",
                "step_config": step.step_config,
                "step_index": step.step_index
            }
        )
        
        # Get tool for this step
        tool_name = step.step_config["step"]
        tool_class = self.tool_registry.get_tool(tool_name)
        
        if not tool_class:
            raise ValueError(f"Tool '{tool_name}' not found in registry")
        
        # Create tool instance
        tool_instance = tool_class(self.db, session.tenant_id)
        
        # Prepare input data (context from previous steps)
        input_data = await self._prepare_step_input(session, step)
        step.input_data = input_data
        
        # Execute tool
        start_time = datetime.utcnow()
        try:
            output_data = await tool_instance.execute(input_data)
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Store tool result
            tool_result = ToolResult(
                session_id=session.id,
                step_id=step.id,
                tenant_id=session.tenant_id,
                tool_name=tool_name,
                input_parameters=input_data,
                output_data=output_data,
                success=True,
                execution_time_ms=int(execution_time)
            )
            self.db.add(tool_result)
            
            # Update step
            step.output_data = output_data
            step.status = StepStatus.COMPLETED
            step.completed_at = datetime.utcnow()
            
            await self._log_thought(
                session.id, 
                step.id,
                "observation", 
                {
                    "message": f"Step completed: {step.step_name}",
                    "execution_time_ms": int(execution_time),
                    "output_summary": self._summarize_output(output_data)
                }
            )
            
            self.db.commit()
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Store failed tool result
            tool_result = ToolResult(
                session_id=session.id,
                step_id=step.id,
                tenant_id=session.tenant_id,
                tool_name=tool_name,
                input_parameters=input_data,
                success=False,
                error_message=str(e),
                execution_time_ms=int(execution_time)
            )
            self.db.add(tool_result)
            
            # Update step
            step.status = StepStatus.FAILED
            step.error_message = str(e)
            step.completed_at = datetime.utcnow()
            
            self.db.commit()
            raise
    
    async def _prepare_step_input(self, session: AgentSession, step: ExecutionStep) -> Dict[str, Any]:
        """Prepare input data for step execution from previous results"""
        input_data = {
            "session_id": str(session.id),
            "step_config": step.step_config,
            "session_context": {
                "invoice_unique_id": session.invoice_unique_id,
                "source_type": session.source_type,
                "source_metadata": session.source_metadata
            }
        }
        
        # Add results from previous steps
        previous_results = {}
        for prev_step in session.execution_steps[:step.step_index]:
            if prev_step.status == StepStatus.COMPLETED and prev_step.output_data:
                previous_results[prev_step.step_name] = prev_step.output_data
        
        input_data["previous_results"] = previous_results
        return input_data
    
    def _summarize_output(self, output_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of output data for logging"""
        if not output_data:
            return {}
        
        summary = {}
        for key, value in output_data.items():
            if isinstance(value, (str, int, float, bool)):
                summary[key] = value
            elif isinstance(value, (list, dict)):
                summary[f"{key}_count"] = len(value) if hasattr(value, '__len__') else 1
            else:
                summary[f"{key}_type"] = type(value).__name__
        
        return summary
    
    async def _handle_step_error(self, session: AgentSession, step: ExecutionStep, error: Exception):
        """Handle step execution error with retry logic"""
        await self._log_thought(
            session.id, 
            step.id,
            "observation", 
            {
                "message": f"Step failed: {step.step_name}",
                "error": str(error),
                "retry_count": session.retry_count,
                "max_retries": session.max_retries
            }
        )
        
        # Check if this is a duplicate error (no retry)
        if isinstance(error, DuplicateInvoiceError):
            session.status = SessionStatus.FAILED
            session.requires_human_review = True
            session.action_item_reason = f"Duplicate invoice detected: {str(error)}"
            session.completed_at = datetime.utcnow()
            self.db.commit()
            return
        
        # Check retry logic
        if session.retry_count < session.max_retries:
            session.retry_count += 1
            session.current_step_index = 0  # Restart from beginning
            
            await self._log_thought(
                session.id, 
                None,
                "decision", 
                {
                    "message": f"Retrying session (attempt {session.retry_count + 1})",
                    "reason": str(error)
                }
            )
            
            # Reset all steps to pending
            for exec_step in session.execution_steps:
                exec_step.status = StepStatus.PENDING
                exec_step.started_at = None
                exec_step.completed_at = None
                exec_step.error_message = None
            
            self.db.commit()
        else:
            # Max retries reached
            session.status = SessionStatus.FAILED
            session.requires_human_review = True
            session.action_item_reason = f"Max retries ({session.max_retries}) reached. Last error: {str(error)}"
            session.completed_at = datetime.utcnow()
            self.db.commit()
    
    async def _handle_session_error(self, session: AgentSession, error: Exception):
        """Handle session-level errors"""
        session.status = SessionStatus.FAILED
        session.requires_human_review = True
        session.action_item_reason = f"Session error: {str(error)}"
        session.completed_at = datetime.utcnow()
        
        await self._log_thought(
            session.id, 
            None,
            "observation", 
            {
                "message": "Session failed with error",
                "error": str(error),
                "traceback": traceback.format_exc()
            }
        )
        
        self.db.commit()
        logger.error(f"Session {session.id} failed: {error}")
    
    async def _log_thought(
        self, 
        session_id: str, 
        step_id: Optional[str],
        thought_type: str, 
        content: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ):
        """Log AI agent thought process"""
        # Get next sequence number
        last_thought = self.db.query(ThoughtChain).filter(
            ThoughtChain.session_id == session_id
        ).order_by(ThoughtChain.sequence_number.desc()).first()
        
        sequence_number = (last_thought.sequence_number + 1) if last_thought else 1
        
        # Get session for tenant_id
        session = self.db.query(AgentSession).filter(AgentSession.id == session_id).first()
        
        thought = ThoughtChain(
            session_id=uuid.UUID(session_id) if isinstance(session_id, str) else session_id,
            step_id=uuid.UUID(step_id) if step_id and isinstance(step_id, str) else step_id,
            tenant_id=session.tenant_id,
            sequence_number=sequence_number,
            thought_type=thought_type,
            content=content,
            context=context or {}
        )

        self.db.add(thought)
        self.db.commit()

    def _log_thought_sync(self, session_id: str, step_id: Optional[str], thought_type: str, content: Dict[str, Any]):
        """Log a thought (sync version)"""
        # Get next sequence number
        max_seq = self.db.query(func.max(ThoughtChain.sequence_number)).filter(
            ThoughtChain.session_id == session_id
        ).scalar() or 0

        thought = ThoughtChain(
            session_id=session_id,
            step_id=step_id,
            tenant_id=self.db.query(AgentSession).filter(AgentSession.id == session_id).first().tenant_id,
            sequence_number=max_seq + 1,
            thought_type=thought_type,
            content=content
        )

        self.db.add(thought)
        self.db.commit()

    def _handle_step_error_sync(self, session: AgentSession, step: ExecutionStep, error: Exception):
        """Handle step execution error with retry logic (sync version)"""
        self._log_thought_sync(
            session.id,
            step.id,
            "observation",
            {
                "message": f"Step failed: {step.step_name}",
                "error": str(error),
                "retry_count": session.retry_count,
                "max_retries": session.max_retries
            }
        )

        # Check if this is a duplicate error (no retry)
        if isinstance(error, DuplicateInvoiceError):
            session.status = SessionStatus.FAILED
            session.requires_human_review = True
            session.action_item_reason = f"Duplicate invoice detected: {str(error)}"
            session.completed_at = datetime.utcnow()
            self.db.commit()
            return

        # Check retry logic
        if session.retry_count < session.max_retries:
            session.retry_count += 1
            session.current_step_index = 0  # Restart from beginning

            self._log_thought_sync(
                session.id,
                None,
                "decision",
                {
                    "message": f"Retrying session (attempt {session.retry_count + 1})",
                    "reason": str(error)
                }
            )
        else:
            # Max retries reached
            session.status = SessionStatus.FAILED
            session.requires_human_review = True
            session.action_item_reason = f"Max retries reached: {str(error)}"
            session.completed_at = datetime.utcnow()

            self._log_thought_sync(
                session.id,
                None,
                "observation",
                {
                    "message": "Session failed after max retries",
                    "final_error": str(error),
                    "retry_count": session.retry_count
                }
            )

        self.db.commit()

    def _handle_session_error_sync(self, session: AgentSession, error: Exception):
        """Handle session-level error (sync version)"""
        logger.error(f"Session {session.id} failed with error: {error}")

        session.status = SessionStatus.FAILED
        session.requires_human_review = True
        session.action_item_reason = f"Session error: {str(error)}"
        session.completed_at = datetime.utcnow()

        self._log_thought_sync(
            session.id,
            None,
            "observation",
            {
                "message": "Session failed with error",
                "error": str(error),
                "error_type": type(error).__name__
            }
        )

        self.db.commit()


class DuplicateInvoiceError(Exception):
    """Exception raised when a duplicate invoice is detected"""
    pass
